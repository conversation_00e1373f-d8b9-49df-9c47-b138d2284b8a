"use client";

import React, { useEffect, useState } from 'react';
import { formatEuro } from '~/utils/format/numbers';
import type { FinanceDashboardData } from '~/app/api/finance-dashboard/route';
import ContractTypeCard from './ContractTypeCard';
import RealtimeWidget from '~/app/(app)/component/RealtimeWidget';
import { MdOutlineEuroSymbol } from 'react-icons/md';

const FinanceDashboard: React.FC = () => {
  const [data, setData] = useState<FinanceDashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/finance-dashboard');
        
        if (!response.ok) {
          throw new Error('Failed to fetch finance dashboard data');
        }
        
        const result = await response.json();
        setData(result);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-lg text-gray-600 dark:text-gray-400">
          Lade Finanzdaten...
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-lg text-red-600 dark:text-red-400">
          Fehler beim Laden der Daten: {error}
        </div>
      </div>
    );
  }

  if (!data) {
    return (
      <div className="flex h-64 items-center justify-center">
        <div className="text-lg text-gray-600 dark:text-gray-400">
          Keine Daten verfügbar
        </div>
      </div>
    );
  }

  const totalOutstanding = data.emp.totalAmount + data.cpo.totalAmount + data.employee.totalAmount;
  const totalInvoices = data.emp.invoices.length + data.cpo.invoices.length + data.employee.invoices.length;

  return (
    <div className="w-full">
      {/* Summary Cards */}
      <div className="mb-6 grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-3">
        <RealtimeWidget
          caption="Gesamte offene Forderungen"
          primaryValue={formatEuro(totalOutstanding)}
          loading={false}
          icon={<MdOutlineEuroSymbol size={21} />}
        />
        <RealtimeWidget
          caption="Anzahl offene Rechnungen"
          primaryValue={totalInvoices}
          loading={false}
          icon={<MdOutlineEuroSymbol size={21} />}
        />
        <RealtimeWidget
          caption="Durchschnittsbetrag"
          primaryValue={totalInvoices > 0 ? formatEuro(totalOutstanding / totalInvoices) : formatEuro(0)}
          loading={false}
          icon={<MdOutlineEuroSymbol size={21} />}
        />
      </div>

      {/* Contract Type Cards */}
      <div className="space-y-6">
        <ContractTypeCard
          title="EMP Verträge"
          icon="🔌"
          totalAmount={data.emp.totalAmount}
          invoices={data.emp.invoices}
          color="text-blue-600 dark:text-blue-400"
        />
        
        <ContractTypeCard
          title="CPO Verträge"
          icon="🏢"
          totalAmount={data.cpo.totalAmount}
          invoices={data.cpo.invoices}
          color="text-green-600 dark:text-green-400"
        />
        
        <ContractTypeCard
          title="Mitarbeiter/Club"
          icon="👥"
          totalAmount={data.employee.totalAmount}
          invoices={data.employee.invoices}
          color="text-purple-600 dark:text-purple-400"
        />
      </div>
    </div>
  );
};

export default FinanceDashboard;
