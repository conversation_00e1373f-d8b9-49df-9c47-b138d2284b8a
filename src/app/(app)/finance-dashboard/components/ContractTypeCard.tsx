"use client";

import React, { useState, useMemo } from 'react';
import { formatEuro } from '~/utils/format/numbers';
import type { FinanceDashboardInvoice } from '~/app/api/finance-dashboard/route';
import InvoiceTable from './InvoiceTable';

interface ContractTypeCardProps {
  title: string;
  icon: string;
  totalAmount: number;
  invoices: FinanceDashboardInvoice[];
  color: string;
}

type FilterType = 'all' | 'current' | 'last' | 'older';

const ContractTypeCard: React.FC<ContractTypeCardProps> = ({
  title,
  icon,
  totalAmount,
  invoices,
  color
}) => {
  const [activeFilter, setActiveFilter] = useState<FilterType>('all');

  const filteredData = useMemo(() => {
    const now = new Date();
    const currentMonth = now.getMonth();
    const currentYear = now.getFullYear();

    const lastMonth = currentMonth === 0 ? 11 : currentMonth - 1;
    const lastMonthYear = currentMonth === 0 ? currentYear - 1 : currentYear;

    const filtered = invoices.filter(invoice => {
      if (!invoice.invoiceDate) return false;

      const invoiceDate = new Date(invoice.invoiceDate);
      const invoiceMonth = invoiceDate.getMonth();
      const invoiceYear = invoiceDate.getFullYear();

      switch (activeFilter) {
        case 'current':
          return invoiceMonth === currentMonth && invoiceYear === currentYear;
        case 'last':
          return invoiceMonth === lastMonth && invoiceYear === lastMonthYear;
        case 'older':
          return (invoiceYear < lastMonthYear) ||
                 (invoiceYear === lastMonthYear && invoiceMonth < lastMonth);
        default:
          return true;
      }
    });

    const filteredAmount = filtered.reduce((sum, inv) => sum + inv.sumGross, 0);

    return {
      invoices: filtered,
      totalAmount: filteredAmount
    };
  }, [invoices, activeFilter]);

  const getFilterButtonClass = (filterType: FilterType) => {
    const baseClass = "px-3 py-1 text-xs font-medium rounded-full transition-colors";
    const activeClass = "bg-primary text-white";
    const inactiveClass = "bg-gray-100 text-gray-600 hover:bg-gray-200 dark:bg-gray-700 dark:text-gray-300 dark:hover:bg-gray-600";

    return `${baseClass} ${activeFilter === filterType ? activeClass : inactiveClass}`;
  };
  return (
    <div className="mb-6 w-full">
      <div className="relative flex min-w-0 flex-col break-words rounded-md bg-white bg-clip-border shadow-md-all dark:bg-gray-950 dark:shadow-soft-dark-xl">
        {/* Header */}
        <div className="rounded-t-md p-4 pb-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <span className="mr-3 text-2xl">{icon}</span>
              <div>
                <h6 className="mb-0 text-lg font-semibold text-primary dark:text-white">
                  {title}
                </h6>
                <p className="mb-0 text-sm text-gray-600 dark:text-gray-400">
                  {filteredData.invoices.length} offene Rechnung{filteredData.invoices.length !== 1 ? 'en' : ''}
                </p>
              </div>
            </div>
            <div className="text-right">
              <h4 className={`mb-0 font-bold ${color}`}>
                {formatEuro(filteredData.totalAmount)}
              </h4>
            </div>
          </div>

          {/* Filter Buttons */}
          <div className="mt-3 flex flex-wrap gap-2">
            <button
              onClick={() => setActiveFilter('all')}
              className={getFilterButtonClass('all')}
            >
              Alle ({invoices.length})
            </button>
            <button
              onClick={() => setActiveFilter('current')}
              className={getFilterButtonClass('current')}
            >
              Aktueller Monat
            </button>
            <button
              onClick={() => setActiveFilter('last')}
              className={getFilterButtonClass('last')}
            >
              Letzter Monat
            </button>
            <button
              onClick={() => setActiveFilter('older')}
              className={getFilterButtonClass('older')}
            >
              Älter
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex-auto p-4 pt-0">
          {filteredData.invoices.length > 0 ? (
            <InvoiceTable invoices={filteredData.invoices} />
          ) : (
            <div className="py-8 text-center text-gray-500 dark:text-gray-400">
              <p>
                {activeFilter === 'all'
                  ? 'Keine offenen Rechnungen'
                  : `Keine offenen Rechnungen für den gewählten Zeitraum`
                }
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ContractTypeCard;
