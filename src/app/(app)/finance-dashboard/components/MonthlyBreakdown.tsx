"use client";

import React from 'react';
import { formatEuro } from '~/utils/format/numbers';
import type { MonthlyOutstanding } from '~/app/api/finance-dashboard/route';

interface MonthlyBreakdownProps {
  monthlyData: MonthlyOutstanding[];
}

const MonthlyBreakdown: React.FC<MonthlyBreakdownProps> = ({ monthlyData }) => {
  if (monthlyData.length === 0) {
    return (
      <div className="mb-6 w-full">
        <div className="relative flex min-w-0 flex-col break-words rounded-md bg-white bg-clip-border shadow-md-all dark:bg-gray-950 dark:shadow-soft-dark-xl">
          <div className="rounded-t-md p-4 pb-2">
            <h6 className="mb-0 text-lg font-semibold text-primary dark:text-white">
              📅 Offene Forderungen nach Monaten
            </h6>
          </div>
          <div className="flex-auto p-4 pt-0">
            <div className="py-8 text-center text-gray-500 dark:text-gray-400">
              <p><PERSON><PERSON> offene<PERSON></p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="mb-6 w-full">
      <div className="relative flex min-w-0 flex-col break-words rounded-md bg-white bg-clip-border shadow-md-all dark:bg-gray-950 dark:shadow-soft-dark-xl">
        {/* Header */}
        <div className="rounded-t-md p-4 pb-2">
          <h6 className="mb-0 text-lg font-semibold text-primary dark:text-white">
            📅 Offene Forderungen nach Monaten
          </h6>
          <p className="mb-0 text-sm text-gray-600 dark:text-gray-400">
            Aufschlüsselung nach Rechnungsmonat
          </p>
        </div>

        {/* Content */}
        <div className="flex-auto p-4 pt-0">
          <div className="overflow-x-auto">
            <table className="w-full table-auto">
              <thead>
                <tr className="border-b border-gray-200 dark:border-gray-700">
                  <th className="px-3 py-2 text-left text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                    Monat
                  </th>
                  <th className="px-3 py-2 text-center text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                    Anzahl Rechnungen
                  </th>
                  <th className="px-3 py-2 text-right text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                    Gesamtbetrag
                  </th>
                  <th className="px-3 py-2 text-center text-xs font-medium uppercase tracking-wider text-gray-500 dark:text-gray-400">
                    Anteil
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200 dark:divide-gray-700">
                {monthlyData.map((monthData) => {
                  const totalAmount = monthlyData.reduce((sum, item) => sum + item.totalAmount, 0);
                  const percentage = totalAmount > 0 ? (monthData.totalAmount / totalAmount * 100) : 0;
                  
                  return (
                    <tr key={monthData.month} className="transition-colors hover:bg-gray-50 dark:hover:bg-gray-800">
                      <td className="px-3 py-3">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {monthData.monthName}
                        </div>
                      </td>
                      <td className="px-3 py-3 text-center">
                        <div className="text-sm text-gray-900 dark:text-white">
                          {monthData.invoiceCount}
                        </div>
                      </td>
                      <td className="px-3 py-3 text-right">
                        <div className="text-sm font-medium text-gray-900 dark:text-white">
                          {formatEuro(monthData.totalAmount)}
                        </div>
                      </td>
                      <td className="px-3 py-3 text-center">
                        <div className="flex items-center justify-center">
                          <div className="mr-2 text-sm text-gray-900 dark:text-white">
                            {percentage.toFixed(1)}%
                          </div>
                          <div className="h-2 w-16 rounded-full bg-gray-200 dark:bg-gray-700">
                            <div
                              className="h-2 rounded-full bg-primary"
                              style={{ width: `${Math.min(percentage, 100)}%` }}
                            />
                          </div>
                        </div>
                      </td>
                    </tr>
                  );
                })}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};

export default MonthlyBreakdown;
