import React from "react";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role } from "@prisma/client";
import NotFound from "~/app/(app)/not-found";
import Headline from "~/component/Headline";
import FinanceDashboard from "./components/FinanceDashboard";

const Page = async () => {
  const session = await getServerSession(authOptions);

  if (!session || (session?.user?.role !== Role.ADMIN && session?.user?.role !== Role.CPO)) {
    return <NotFound />;
  }

  return (
    <>
      <Headline title="Finance Dashboard" />
      <div className="mb-4">
        <p className="text-gray-600 dark:text-gray-400">
          Übersicht über alle offenen Rechnungen nach Vertragstypen. 
          Farbkodierung: <span className="text-yellow-600">Gelb (>14 Tage)</span>, 
          <span className="text-orange-600"> Orange (>30 Tage)</span>, 
          <span className="text-red-600"> Rot (>60 Tage)</span>
        </p>
      </div>
      <FinanceDashboard />
    </>
  );
};

export default Page;
