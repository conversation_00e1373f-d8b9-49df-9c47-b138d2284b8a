import type { NextRequest } from "next/server";
import prisma from "../../../server/db/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role, StateOfInvoice } from "@prisma/client";
import { getOusBelowOu } from "~/server/model/ou/func";

export type FinanceDashboardData = {
  emp: {
    totalAmount: number;
    invoices: FinanceDashboardInvoice[];
  };
  cpo: {
    totalAmount: number;
    invoices: FinanceDashboardInvoice[];
  };
  employee: {
    totalAmount: number;
    invoices: FinanceDashboardInvoice[];
  };
};

export type FinanceDashboardInvoice = {
  id: string;
  invoiceNumber: string | null;
  sumGross: number;
  invoiceDate: Date | null;
  sentDate: Date | null;
  daysSinceSent: number | null;
  contactName: string | null;
  userName: string | null;
  contractName: string | null;
  urgencyLevel: 'normal' | 'warning' | 'urgent' | 'critical';
};

function calculateUrgencyLevel(daysSinceSent: number | null): 'normal' | 'warning' | 'urgent' | 'critical' {
  if (daysSinceSent === null) return 'normal';
  if (daysSinceSent > 60) return 'critical';
  if (daysSinceSent > 30) return 'urgent';
  if (daysSinceSent > 14) return 'warning';
  return 'normal';
}

function calculateDaysSinceSent(sentDate: Date | null): number | null {
  if (!sentDate) return null;
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - sentDate.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

function extractSentDateFromHistory(history: string | null): Date | null {
  if (!history) return null;
  
  // Look for patterns like "Invoice are send to" or "Mail sent to" in history
  const lines = history.split('\n');
  for (const line of lines) {
    if (line.includes('Invoice are send to') || line.includes('Mail sent to')) {
      // Extract date from beginning of line (format: "DD.MM.YYYY, HH:MM:SS")
      const dateMatch = line.match(/^(\d{1,2}\.\d{1,2}\.\d{4},\s\d{1,2}:\d{2}:\d{2})/);
      if (dateMatch) {
        try {
          const [datePart, timePart] = dateMatch[1].split(', ');
          const [day, month, year] = datePart.split('.');
          const [hour, minute, second] = timePart.split(':');
          return new Date(
            parseInt(year),
            parseInt(month) - 1,
            parseInt(day),
            parseInt(hour),
            parseInt(minute),
            parseInt(second)
          );
        } catch (error) {
          console.error('Error parsing date from history:', error);
        }
      }
    }
  }
  return null;
}

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || (session?.user?.role !== Role.ADMIN && session?.user?.role !== Role.CPO)) {
    return new Response("no auth", { status: 401 });
  }

  try {
    // Get user's OU and all OUs below it
    const userOuId = session.user.selectedOu?.id;
    let ouIds: string[] = [];
    
    if (userOuId) {
      const ous = await getOusBelowOu(userOuId);
      ouIds = ous.map(ou => ou.id);
    }

    // Get all unpaid invoices that are CREATED (sent) but not PAID
    const unpaidInvoices = await prisma.invoice.findMany({
      where: {
        stateOfInvoice: StateOfInvoice.CREATED,
        paidOnDate: null,
        sendAsMail: true,
        ...(ouIds.length > 0 && {
          OR: [
            {
              contact: {
                ouId: {
                  in: ouIds
                }
              }
            },
            {
              user: {
                ouId: {
                  in: ouIds
                }
              }
            },
            {
              contract: {
                contact: {
                  ouId: {
                    in: ouIds
                  }
                }
              }
            }
          ]
        })
      },
      include: {
        contact: true,
        user: true,
        contract: {
          include: {
            contact: true
          }
        }
      },
      orderBy: {
        invoiceDate: 'desc'
      }
    });

    const empInvoices: FinanceDashboardInvoice[] = [];
    const cpoInvoices: FinanceDashboardInvoice[] = [];
    const employeeInvoices: FinanceDashboardInvoice[] = [];

    for (const invoice of unpaidInvoices) {
      const sentDate = extractSentDateFromHistory(invoice.history);
      const daysSinceSent = calculateDaysSinceSent(sentDate);
      const urgencyLevel = calculateUrgencyLevel(daysSinceSent);

      const dashboardInvoice: FinanceDashboardInvoice = {
        id: invoice.id,
        invoiceNumber: invoice.invoiceNumber,
        sumGross: invoice.sumGross,
        invoiceDate: invoice.invoiceDate,
        sentDate,
        daysSinceSent,
        contactName: invoice.contact?.companyName || invoice.contact?.name || null,
        userName: invoice.user ? `${invoice.user.firstName} ${invoice.user.lastName}` : null,
        contractName: invoice.contract?.name || null,
        urgencyLevel
      };

      // Categorize by contract type
      if (invoice.userId) {
        // Employee/Club invoice
        employeeInvoices.push(dashboardInvoice);
      } else if (invoice.contractId) {
        // CPO invoice
        cpoInvoices.push(dashboardInvoice);
      } else if (invoice.contactId && !invoice.contact?.cpo) {
        // EMP invoice (contact but not CPO)
        empInvoices.push(dashboardInvoice);
      }
    }

    const result: FinanceDashboardData = {
      emp: {
        totalAmount: empInvoices.reduce((sum, inv) => sum + inv.sumGross, 0),
        invoices: empInvoices
      },
      cpo: {
        totalAmount: cpoInvoices.reduce((sum, inv) => sum + inv.sumGross, 0),
        invoices: cpoInvoices
      },
      employee: {
        totalAmount: employeeInvoices.reduce((sum, inv) => sum + inv.sumGross, 0),
        invoices: employeeInvoices
      }
    };

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching finance dashboard data:', error);
    return new Response("Internal Server Error", { status: 500 });
  }
}
