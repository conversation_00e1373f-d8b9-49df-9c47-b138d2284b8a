import type { NextRequest } from "next/server";
import prisma from "../../../server/db/prisma";
import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "~/app/api/auth/[...nextauth]/route";
import { Role, StateOfInvoice, KindOfInvoice } from "@prisma/client";
import { getOusBelowOu } from "~/server/model/ou/func";

export type FinanceDashboardData = {
  emp: {
    totalAmount: number;
    invoices: FinanceDashboardInvoice[];
  };
  cpo: {
    totalAmount: number;
    invoices: FinanceDashboardInvoice[];
  };
  employee: {
    totalAmount: number;
    invoices: FinanceDashboardInvoice[];
  };
};

export type FinanceDashboardInvoice = {
  id: string;
  invoiceNumber: string | null;
  sumGross: number;
  invoiceDate: Date | null;
  sentDate: Date | null;
  sentTo: string | null;
  daysSinceSent: number | null;
  contactName: string | null;
  userName: string | null;
  contractName: string | null;
  urgencyLevel: 'normal' | 'warning' | 'urgent' | 'critical';
};

function calculateUrgencyLevel(daysSinceSent: number | null): 'normal' | 'warning' | 'urgent' | 'critical' {
  if (daysSinceSent === null) return 'normal';
  if (daysSinceSent > 60) return 'critical';
  if (daysSinceSent > 30) return 'urgent';
  if (daysSinceSent > 14) return 'warning';
  return 'normal';
}

function calculateDaysSinceSent(invoiceDate: Date | null): number | null {
  if (!invoiceDate) return null;
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - invoiceDate.getTime());
  return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
}

export async function GET(request: NextRequest) {
  const session = await getServerSession(authOptions);

  if (!session || (session?.user?.role !== Role.ADMIN && session?.user?.role !== Role.CPO)) {
    return new Response("no auth", { status: 401 });
  }

  try {
    // Get user's OU and all OUs below it
    const userOuId = session.user.selectedOu?.id;
    let ouIds: string[] = [];

    if (userOuId) {
      const ous = await getOusBelowOu(userOuId);
      ouIds = ous.map(ou => ou.id).filter(id => id !== undefined && id !== null);
    }

    // Build where clause
    const whereClause: any = {
      stateOfInvoice: StateOfInvoice.CREATED,
      paidOnDate: null,
      OR: [
        { sendAsMail: true },
        { history: { contains: 'Only marked as sent' } },
        { history: { contains: 'Mail sent to' } },
        { history: { contains: 'Invoice are send to' } },
        { history: { contains: 'CDRs are send to' } },
        { history: { contains: 'Invoice and CDRs are send to' } }
      ],
      // Exclude cancelled invoices and storno invoices
      kindOfInvoice: {
        notIn: [KindOfInvoice.STORNO, KindOfInvoice.CREDIT_STORNO]
      },
      // Exclude invoices that have been cancelled by a storno
      invoiceChilds: {
        none: {
          kindOfInvoice: {
            in: [KindOfInvoice.STORNO, KindOfInvoice.CREDIT_STORNO]
          }
        }
      }
    };

    // Only add OU filter if we have valid OU IDs
    if (ouIds.length > 0) {
      // We need to combine the existing OR with the OU filter using AND
      const ouFilter = {
        OR: [
          {
            contact: {
              ouId: {
                in: ouIds
              }
            }
          },
          {
            user: {
              ouId: {
                in: ouIds
              }
            }
          },
          {
            contract: {
              contact: {
                ouId: {
                  in: ouIds
                }
              }
            }
          }
        ]
      };

      // Combine with existing conditions using AND
      whereClause.AND = [
        {
          OR: whereClause.OR // Keep the mail sent conditions
        },
        ouFilter
      ];
      delete whereClause.OR; // Remove the top-level OR since we moved it to AND
    }

    // Get all unpaid invoices that are CREATED (sent) but not PAID
    const unpaidInvoices = await prisma.invoice.findMany({
      where: whereClause,
      include: {
        contact: true,
        user: true,
        contract: {
          include: {
            contact: true
          }
        }
      },
      orderBy: {
        invoiceDate: 'desc'
      }
    });

    const empInvoices: FinanceDashboardInvoice[] = [];
    const cpoInvoices: FinanceDashboardInvoice[] = [];
    const employeeInvoices: FinanceDashboardInvoice[] = [];

    for (const invoice of unpaidInvoices) {
      const sentInfo = extractSentInfoFromHistory(invoice.history);
      const daysSinceSent = calculateDaysSinceSent(sentInfo.date);
      const urgencyLevel = calculateUrgencyLevel(daysSinceSent);

      const dashboardInvoice: FinanceDashboardInvoice = {
        id: invoice.id,
        invoiceNumber: invoice.invoiceNumber,
        sumGross: invoice.sumGross,
        invoiceDate: invoice.invoiceDate,
        sentDate: sentInfo.date,
        sentTo: sentInfo.sentTo,
        daysSinceSent,
        contactName: invoice.contact?.companyName || invoice.contact?.name || null,
        userName: invoice.user ? `${invoice.user.firstName} ${invoice.user.lastName}` : null,
        contractName: invoice.contract?.name || null,
        urgencyLevel
      };

      // Categorize by contract type
      if (invoice.userId) {
        // Employee/Club invoice
        employeeInvoices.push(dashboardInvoice);
      } else if (invoice.contractId) {
        // CPO invoice
        cpoInvoices.push(dashboardInvoice);
      } else if (invoice.contactId && !invoice.contact?.cpo) {
        // EMP invoice (contact but not CPO)
        empInvoices.push(dashboardInvoice);
      }
    }

    const result: FinanceDashboardData = {
      emp: {
        totalAmount: empInvoices.reduce((sum, inv) => sum + inv.sumGross, 0),
        invoices: empInvoices
      },
      cpo: {
        totalAmount: cpoInvoices.reduce((sum, inv) => sum + inv.sumGross, 0),
        invoices: cpoInvoices
      },
      employee: {
        totalAmount: employeeInvoices.reduce((sum, inv) => sum + inv.sumGross, 0),
        invoices: employeeInvoices
      }
    };

    return NextResponse.json(result);
  } catch (error) {
    console.error('Error fetching finance dashboard data:', error);
    return new Response("Internal Server Error", { status: 500 });
  }
}
