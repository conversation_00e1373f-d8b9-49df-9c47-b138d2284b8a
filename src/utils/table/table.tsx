"use client";
import { AgGridReact } from "ag-grid-react";
import { type AgGridView } from "@prisma/client";
import { ReactElement } from "react";
import React, { useEffect, useState } from "react";

import "ag-grid-community/dist/styles/ag-grid.css";
import "ag-grid-community/dist/styles/ag-theme-alpine.css";
import "~/styles/ag-theme-eulektro.css";
import { ChartModel, LicenseManager } from "ag-grid-enterprise";
import type { ColGroupDef, ColDef, GridOptions } from "ag-grid-community";
import type { ColumnApi, GridApi, GridReadyEvent } from "ag-grid-community";
import { FiMaximize2, FiColumns, FiZoomIn } from "react-icons/fi";
import Button from "~/component/button";
import { FaPlus, FaRedo } from "react-icons/fa";
import InputDialog from "~/app/(app)/util/InputDialog";
import Dropdown from "~/app/(app)/util/Dropdown";
import { GrPowerReset } from "react-icons/gr";

LicenseManager.setLicenseKey(
  "CompanyName=Zeitgleich GmbH,LicensedApplication=BEAST,LicenseType=SingleApplication,LicensedConcurrentDeveloperCount=1,LicensedProductionInstancesCount=0,AssetReference=AG-016007,ExpiryDate=2_July_2022_[v2]_MTY1NjcxNjQwMDAwMA==579f3e57c0b0b4db77e0428d0cac15be",
);

interface Props {
  gridId?: string;
  gridOptions?: GridOptions;
  columnDefs?: (ColDef | ColGroupDef)[] | null;
  rowData?: any;
  headerLeft?: ReactElement;
  headerCenter?: ReactElement;
  headerRight?: ReactElement;
  isRowSelectable?: (row: any) => boolean;
  onSelectionChanged?: () => void;
  groupIncludeTotalFooter?: boolean;
  getRowId?: (data: any) => string;
  groupIncludeFooter?: boolean;
  animateRows?: boolean;
  onGridReady?: (params: GridReadyEvent) => void;
  onFirstDataRendered?: (params: GridReadyEvent) => void;
  rowSelection?: "single" | "multiple";
  overlayNoRowsTemplate?: string;
  onRowDataUpdated?: (params: GridReadyEvent) => void;
}

export const filterParams = {
  comparator: (filterLocalDateAtMidnight: Date, cellValue: string) => {
    const dateAsString = cellValue;
    if (dateAsString == null) return -1;
    const cellDate = new Date(dateAsString);

    if (typeof filterLocalDateAtMidnight == "string") {
      console.warn(
        "Col is type string, expected Data - forgot to set filter: agDateColumnFilter ?",
      );
      filterLocalDateAtMidnight = new Date(filterLocalDateAtMidnight);
    }
    if (filterLocalDateAtMidnight.getTime() === cellDate.getTime()) {
      return 0;
    }

    if (cellDate < filterLocalDateAtMidnight) {
      return -1;
    }

    if (cellDate > filterLocalDateAtMidnight) {
      return 1;
    }
  },
  browserDatePicker: true,
  minValidYear: 2022,
  maxValidYear: 2030,
};

const Table = ({
  gridOptions = {},
  gridId,
  columnDefs,
  rowData,
  headerLeft,
  headerCenter,
  headerRight,
  isRowSelectable,
  onSelectionChanged,
  groupIncludeTotalFooter = false,
  getRowId,
  groupIncludeFooter = false,
  animateRows = false,
  onGridReady,
  onFirstDataRendered,
  rowSelection,
  overlayNoRowsTemplate,
  onRowDataUpdated,
}: Props) => {
  const [gridApi, setGridApi] = useState<GridApi | undefined>();
  const [columApi, setColumnApi] = useState<ColumnApi | undefined>();
  const [showViewInputModal, setShowViewInputModal] = useState<boolean>(false);
  const [views, setViews] = useState<AgGridView[]>([]);
  const [selectedViewId, setSelectedViewId] = useState<string | undefined>();
  const [internalChange, setInternalChange] = useState<boolean>(false);

  useEffect(() => {
    void fetchViews("", true);
  }, []);

  useEffect(() => {
    sizeToFit();
  }, [rowData]);

  const defaultBackendGridOptions: GridOptions = {
    rowModelType: "clientSide",
    enableRangeSelection: true,
    defaultColDef: {
      sortable: true,
      enableRowGroup: true,
      floatingFilter: true,
      filter: true,
      resizable: true,
      editable: true,
      filterParams: {
        buttons: ["reset", "apply"],
      },
      menuTabs: ["columnsMenuTab"],
    },
    rowHeight: 32,
    sideBar: {
      toolPanels: [
        {
          id: "columns",
          labelDefault: "Columns",
          labelKey: "columns",
          iconKey: "columns",
          toolPanel: "agColumnsToolPanel",
          minWidth: 225,
          maxWidth: 225,
          width: 225,
        },
        {
          id: "filters",
          labelDefault: "Filters",
          labelKey: "filters",
          iconKey: "filter",
          toolPanel: "agFiltersToolPanel",
          minWidth: 180,
          maxWidth: 400,
          width: 250,
        },
      ],
      position: "right",
      defaultToolPanel: "",
    },
    ...gridOptions,
  };

  function sizeToFit() {
    if (gridApi) {
      gridApi!.sizeColumnsToFit();
    }
  }

  function autoSizeAll(skipHeader: boolean) {
    if (columApi) {
      columApi!.autoSizeAllColumns(skipHeader);
    }
  }

  const handleGridReady = (params: GridReadyEvent) => {
    setGridApi(params.api);
    setColumnApi(params.columnApi);
    sizeToFit();

    if (onGridReady) {
      onGridReady(params);
    }
  };
  const onSaveState = (value: string, ovveride = false) => {
    setShowViewInputModal(false);
    void saveTableState(value, ovveride);
  };

  const saveTableState = async (value: string, overwrite: boolean) => {
    if (gridApi && columApi) {
      if (gridId) {
        const columnState = columApi.getColumnState();
        const filterModel = gridApi.getFilterModel();
        const chartModels = gridApi.getChartModels();

        const saveViewReponse = await fetch("/api/views", {
          method: "POST",
          body: JSON.stringify({
            overwrite: overwrite,
            name: value,
            viewId: views.find((view) => view.name === value)?.id ?? "",
            gridId: gridId,
            columnState: JSON.stringify(columnState),
            filterModel: JSON.stringify(filterModel),
            chartModels: JSON.stringify(chartModels),
          }),
        });
        try {
          if (saveViewReponse.ok) {
            void fetchViews(value);
          }
        } catch (e) {
          console.log("error");
        }
      }
    }
  };
  useEffect(() => {
    //show first view on init
    if (views && views[0] && !selectedViewId) {
      onViewChanged(views[0].id);
    }
  }, [views]);

  const fetchViews = async (lasteSaved = "", firstRender = false) => {
    const viewsResponse = await fetch(`/api/views?gridId=${gridId}`, {
      method: "GET",
    });
    if (viewsResponse.ok) {
      const response = await viewsResponse.json();

      if (lasteSaved) {
        setSelectedViewId(response.find((view: AgGridView) => view.name === lasteSaved)?.id);
      }

      setViews(response);
    }
  };
  const onViewDelete = async (id: string) => {
    if (confirm("Ansicht wirklich löschen?")) {
      const viewsResponse = await fetch(`/api/views?id=${id}`, {
        method: "DELETE",
      });
      if (viewsResponse.ok) {
        const response = await viewsResponse.json();
        setViews(response);
      }
    }
  };

  const onViewChanged = (id: string) => {
    const view = views?.find((view) => view.id === id);

    if (view && gridApi && columApi) {
      setSelectedViewId(view.id);
      setInternalChange(true);
      columApi.applyColumnState({ state: JSON.parse(view.columnState) });

      // clear the grid in case charts have been used in last view
      const currentCharts = gridApi.getChartModels() ?? [];
      for (const chart of currentCharts) {
        gridApi.getChartRef(chart.chartId)?.destroyChart();
      }
      const charts: ChartModel[] = view.chartModels != "" ? JSON.parse(view.chartModels) : [];
      for (const chart of charts) {
        gridApi.createRangeChart({ ...chart });
      }
      gridApi.setFilterModel(JSON.parse(view.filterModel));
      gridApi.onFilterChanged();
    }
  };
  const onTableChanged = () => {
    // if change was due to event from aggrid without changing the view
    // manually  by using the dropdown,
    // then set the selected view to "" so no view in selected in dropdown

    if (!internalChange) {
      setSelectedViewId("");
    }
    setInternalChange(false);
  };
  return (
    <div>
      <div className={"my-5 flex w-full flex-col justify-between sm:flex-row"}>
        <div className={"mb-2 flex items-center gap-2 sm:mb-0"}>
          {headerLeft && headerLeft}
          {gridId && (
            <>
              {views?.length > 0 ? (
                <Dropdown
                  small={true}
                  onChange={onViewChanged}
                  onDelete={onViewDelete}
                  options={views?.map((view) => {
                    return { id: view.id, label: view.name, selected: view.id === selectedViewId };
                  })}
                />
              ) : (
                <span className={"text-center font-bold"}>Ansicht hinzufügen:</span>
              )}

              <Button
                small={true}
                title={"Neue Ansicht hinzufügen"}
                className={"mr-2"}
                onClick={() => setShowViewInputModal(true)}
              >
                <FaPlus size={12} />
              </Button>
            </>
          )}
          <Button
            small={true}
            title={"Filter & Spalten zurücksetzen"}
            className={"mr-2"}
            onClick={() => {
              gridApi?.setFilterModel(null);
              columApi?.resetColumnState();
            }}
          >
            <FaRedo size={12} />
          </Button>
        </div>
        {headerCenter && headerCenter}
        <div className={"flex  gap-2 "}>
          <Button
            small={true}
            title={"Zeigt alle Spalten, ohne dass ein Scrollbalken erscheint"}
            onClick={sizeToFit}
          >
            <FiMaximize2 size={12} className={"mr-1"} /> Fit
          </Button>
          <Button
            small={true}
            title={
              "Passt die Spaltenbreite an, sodass die Kopfzeile jeder Spalte zu lesen ist, sowie der Inhalt."
            }
            onClick={() => autoSizeAll(false)}
          >
            <FiZoomIn size={12} className={"mr-1"} /> Auto
          </Button>
          <Button
            small={true}
            title={
              "Zeigt alle Spalten, sodass der Inhalt lesbar ist, die Breite der Kopfzeile wird nicht berücksichtig"
            }
            onClick={() => autoSizeAll(true)}
          >
            <FiColumns size={12} className={"mr-1"} /> Skip Header
          </Button>
        </div>
        {headerRight && headerRight}
      </div>
      <div className="ag-theme-alpine ag-theme-eulektro" style={{ width: "100%", height: "600px" }}>
        <AgGridReact
          onRowDataUpdated={onRowDataUpdated}
          overlayNoRowsTemplate={overlayNoRowsTemplate ?? "Keine Daten"}
          enableCharts={true}
          rowSelection={rowSelection}
          gridOptions={defaultBackendGridOptions}
          onRowDataChanged={(api) => {
            window.setTimeout(() => {
              sizeToFit();
            }, 500); // delay a bit to give cellRenderer change to render
            return api;
          }}
          rowData={rowData}
          columnDefs={columnDefs}
          groupIncludeFooter={groupIncludeFooter}
          groupIncludeTotalFooter={groupIncludeTotalFooter}
          animateRows={animateRows}
          getRowId={getRowId}
          onGridReady={handleGridReady}
          isRowSelectable={isRowSelectable}
          onSelectionChanged={onSelectionChanged}
          onGridSizeChanged={() => sizeToFit()}
          onFirstDataRendered={onFirstDataRendered}
          //onColumnResized={onTableChanged}
          onColumnMoved={onTableChanged}
          onColumnVisible={onTableChanged}
          onSortChanged={onTableChanged}
          onFilterChanged={onTableChanged}
        />
      </div>
      {showViewInputModal && (
        <InputDialog
          message={"Bitte einen Namen für die Ansicht eingeben."}
          title={"Ansicht speichern"}
          onNo={() => setShowViewInputModal(false)}
          onYes={onSaveState}
          yesLabel={"Speichern"}
          noLabel={"Abbrechen"}
          defaultValue={
            views?.find((view) => view?.id === selectedViewId)?.name ?? "keine Ansicht ausgewählt"
          }
          overwriteLabel={"überschreiben"}
        />
      )}
    </div>
  );
};

export default Table;
